# 庞加莱球数据点绘制与网格分析项目

## 项目概述
本项目专注于庞加莱球的数据可视化、经纬网格系统构建、交点选择与优化分析。主要处理偏振态数据在庞加莱球面上的表示和分析。

## 项目状态
- ✅ 基础网格系统构建 (完成)
- ✅ 经纬线选择算法 (完成)
- ✅ 交点优化分析 (完成)
- 🔄 精细化网格分析 (进行中)

## 核心组件

### 主要目录结构
```
multidata_724/                  # 多数据集分析工具
├── grid_based_meridian_parallel_viewer.py    # 网格经纬查看器
├── improved_meridian_parallel_selector.py    # 改进的经纬选择器
├── interactive_meridian_parallel_viewer.py   # 交互式经纬查看器
├── meridian_parallel_selector.py             # 经纬选择器
├── optimized_intersection_viewer.py          # 优化交点查看器
├── selective_data_viewer.py                  # 选择性数据查看器
└── unified_meridian_parallel_viewer.py       # 统一经纬查看器

sk_data/                        # 偏振数据
└── polarization_data.txt       # 偏振态数据文件
```

### 关键配置文件
- `grid_system_info.json` - 网格系统信息
- `meridian_parallel_grid_info.json` - 经纬网格信息
- `optimized_line_catalog.json` - 优化线目录

### 结果数据文件
- `enhanced_selection_results.json` - 增强选择结果
- `improved_selection_results.json` - 改进选择结果
- `optimized_intersection_results.json` - 优化交点结果
- `unified_meridian_parallel_results.json` - 统一经纬结果
- `fine_meridian_selection_results.csv/json` - 精细经线选择结果
- `fine_parallel_selection_results.csv/json` - 精细纬线选择结果
- `meridian_selection_results.csv/json` - 经线选择结果
- `parallel_selection_results.csv/json` - 纬线选择结果
- `ultra_fine_parallel_results.csv/json` - 超精细纬线结果

## 可视化结果

### PDF 报告
- `clean_grid_system_724.pdf` - 清洁网格系统
- `enhanced_grid_724.pdf` - 增强网格
- `fine_meridian_selection_724.pdf` - 精细经线选择
- `fine_parallel_selection_724.pdf` - 精细纬线选择
- `focused_grid_724.pdf` - 聚焦网格
- `grid_based_system_724.pdf` - 基于网格的系统
- `improved_meridian_parallel_724.pdf` - 改进经纬
- `meridian_parallel_grid_724.pdf` - 经纬网格
- `meridian_selection_724.pdf` - 经线选择
- `multi_dataset_poincare_724.pdf` - 多数据集庞加莱球
- `optimized_grid_724.pdf` - 优化网格
- `optimized_intersection_724.pdf` - 优化交点
- `parallel_selection_724.pdf` - 纬线选择
- `poincare_sphere_data724_4.pdf` - 庞加莱球数据
- `refined_poincare_sphere.pdf` - 精细化庞加莱球
- `selected_grid_points_724.pdf` - 选定网格点
- `ultra_fine_parallel_selection_724.pdf` - 超精细纬线选择
- `unified_meridian_parallel_724.pdf` - 统一经纬

### PNG 图像
对应的PNG版本位于 `results/` 目录下，文件名相同但扩展名为 `.png`

## 快速开始

### 基础网格查看
```python
cd multidata_724
python grid_based_meridian_parallel_viewer.py
```

### 交互式经纬分析
```python
python interactive_meridian_parallel_viewer.py
```

### 优化交点分析
```python
python optimized_intersection_viewer.py
```

### 选择性数据查看
```python
python selective_data_viewer.py
```

### 统一经纬查看
```python
python unified_meridian_parallel_viewer.py
```

## 数据处理流程

1. **数据导入**: 从 `sk_data/polarization_data.txt` 读取偏振态数据
2. **网格构建**: 基于庞加莱球面构建经纬网格系统
3. **点选择**: 使用各种算法选择关键网格点和交点
4. **优化分析**: 对选择结果进行优化和精细化
5. **可视化**: 生成PDF和PNG格式的分析报告

## 技术特点
- 基于Python的庞加莱球可视化
- 多种经纬网格选择算法
- 交互式数据分析工具
- 高质量的科学可视化输出
- 支持多数据集对比分析

## 算法组件
- 经线选择算法 (Meridian Selection)
- 纬线选择算法 (Parallel Selection)  
- 交点优化算法 (Intersection Optimization)
- 网格精细化算法 (Grid Refinement)

## 依赖要求
- Python 3.8+
- NumPy, SciPy
- Matplotlib
- Pandas (用于CSV数据处理)
- JSON (用于配置文件)

## 联系信息
项目维护者：[您的姓名]
最后更新：2025-08-11
