# 边带特性增强的光谱损失函数设计方案

## 1. 问题分析

### 1.1 当前问题
- 传统的MSE和皮尔逊相关系数计算对整个光谱进行全局计算
- 无法突出边带特性的重要性
- 边带区域的匹配质量对锁模状态判别至关重要

### 1.2 边带特性的重要性
在锁模激光器中，边带特性反映了：
- 脉冲的时域形状质量
- 锁模状态的稳定性
- 噪声和基座的抑制程度
- 光谱的对称性和纯净度

## 2. 分区域损失函数设计

### 2.1 光谱区域划分策略

将光谱划分为三个区域：
1. **主峰区域 (Core Region)**: 包含主要能量的中心区域
2. **边带区域 (Sideband Region)**: 主峰两侧的过渡区域
3. **基底区域 (Base Region)**: 远离主峰的低强度区域

### 2.2 区域边界自动识别算法

#### 2.2.1 主峰识别
```python
def identify_main_peak(wavelength, intensity):
    """识别主峰位置和范围"""
    # 1. 找到全局最大值位置
    peak_idx = np.argmax(intensity)
    peak_wavelength = wavelength[peak_idx]
    peak_intensity = intensity[peak_idx]
    
    # 2. 计算半高全宽(FWHM)
    half_max = peak_intensity / 2
    left_idx = np.where(intensity[:peak_idx] <= half_max)[0]
    right_idx = np.where(intensity[peak_idx:] <= half_max)[0]
    
    if len(left_idx) > 0:
        left_boundary = left_idx[-1]
    else:
        left_boundary = 0
        
    if len(right_idx) > 0:
        right_boundary = peak_idx + right_idx[0]
    else:
        right_boundary = len(intensity) - 1
    
    return peak_idx, left_boundary, right_boundary
```

#### 2.2.2 边带区域定义
```python
def define_sideband_regions(wavelength, intensity, core_left, core_right):
    """定义边带区域"""
    # 边带区域为主峰FWHM的1.5-3倍范围
    core_width = core_right - core_left
    
    # 左边带
    left_sideband_start = max(0, core_left - 3 * core_width)
    left_sideband_end = core_left
    
    # 右边带
    right_sideband_start = core_right
    right_sideband_end = min(len(intensity) - 1, core_right + 3 * core_width)
    
    return (left_sideband_start, left_sideband_end), (right_sideband_start, right_sideband_end)
```

### 2.3 增强的MSE损失函数

#### 2.3.1 分区域加权MSE
```python
def enhanced_mse_loss(target_spectrum, measured_spectrum, weights):
    """
    增强的MSE损失函数，突出边带特性
    
    Args:
        target_spectrum: 目标光谱 (wavelength, intensity)
        measured_spectrum: 测量光谱 (wavelength, intensity)
        weights: 区域权重 {'core': w1, 'sideband': w2, 'base': w3}
    """
    target_wl, target_int = target_spectrum
    measured_wl, measured_int = measured_spectrum
    
    # 确保波长对齐
    measured_int_interp = np.interp(target_wl, measured_wl, measured_int)
    
    # 识别区域
    peak_idx, core_left, core_right = identify_main_peak(target_wl, target_int)
    (left_sb_start, left_sb_end), (right_sb_start, right_sb_end) = define_sideband_regions(
        target_wl, target_int, core_left, core_right)
    
    # 分区域计算MSE
    # 主峰区域
    core_mse = np.mean((target_int[core_left:core_right] - 
                       measured_int_interp[core_left:core_right])**2)
    
    # 边带区域 (左右边带合并)
    left_sideband_mse = np.mean((target_int[left_sb_start:left_sb_end] - 
                                measured_int_interp[left_sb_start:left_sb_end])**2)
    right_sideband_mse = np.mean((target_int[right_sb_start:right_sb_end] - 
                                 measured_int_interp[right_sb_start:right_sb_end])**2)
    sideband_mse = (left_sideband_mse + right_sideband_mse) / 2
    
    # 基底区域
    base_indices = np.concatenate([
        np.arange(0, left_sb_start),
        np.arange(right_sb_end, len(target_int))
    ])
    base_mse = np.mean((target_int[base_indices] - 
                       measured_int_interp[base_indices])**2)
    
    # 加权总MSE
    total_mse = (weights['core'] * core_mse + 
                 weights['sideband'] * sideband_mse + 
                 weights['base'] * base_mse)
    
    return total_mse, {
        'core_mse': core_mse,
        'sideband_mse': sideband_mse,
        'base_mse': base_mse,
        'regions': {
            'core': (core_left, core_right),
            'left_sideband': (left_sb_start, left_sb_end),
            'right_sideband': (right_sb_start, right_sb_end),
            'base': base_indices
        }
    }
```

### 2.4 增强的皮尔逊相关系数

#### 2.4.1 分区域加权相关系数
```python
def enhanced_pearson_loss(target_spectrum, measured_spectrum, weights):
    """
    增强的皮尔逊相关系数损失函数
    """
    target_wl, target_int = target_spectrum
    measured_wl, measured_int = measured_spectrum
    
    # 确保波长对齐
    measured_int_interp = np.interp(target_wl, measured_wl, measured_int)
    
    # 识别区域
    peak_idx, core_left, core_right = identify_main_peak(target_wl, target_int)
    (left_sb_start, left_sb_end), (right_sb_start, right_sb_end) = define_sideband_regions(
        target_wl, target_int, core_left, core_right)
    
    # 分区域计算相关系数
    from scipy.stats import pearsonr
    
    # 主峰区域相关系数
    core_corr, _ = pearsonr(target_int[core_left:core_right], 
                           measured_int_interp[core_left:core_right])
    
    # 边带区域相关系数
    left_sb_corr, _ = pearsonr(target_int[left_sb_start:left_sb_end], 
                              measured_int_interp[left_sb_start:left_sb_end])
    right_sb_corr, _ = pearsonr(target_int[right_sb_start:right_sb_end], 
                               measured_int_interp[right_sb_start:right_sb_end])
    sideband_corr = (left_sb_corr + right_sb_corr) / 2
    
    # 基底区域相关系数
    base_indices = np.concatenate([
        np.arange(0, left_sb_start),
        np.arange(right_sb_end, len(target_int))
    ])
    base_corr, _ = pearsonr(target_int[base_indices], 
                           measured_int_interp[base_indices])
    
    # 转换为损失 (1 - correlation)
    core_loss = 1 - core_corr if not np.isnan(core_corr) else 1
    sideband_loss = 1 - sideband_corr if not np.isnan(sideband_corr) else 1
    base_loss = 1 - base_corr if not np.isnan(base_corr) else 1
    
    # 加权总损失
    total_loss = (weights['core'] * core_loss + 
                  weights['sideband'] * sideband_loss + 
                  weights['base'] * base_loss)
    
    return total_loss, {
        'core_corr': core_corr,
        'sideband_corr': sideband_corr,
        'base_corr': base_corr,
        'core_loss': core_loss,
        'sideband_loss': sideband_loss,
        'base_loss': base_loss
    }
```

## 3. 权重配置策略

### 3.1 推荐权重配置
```python
# 边带增强配置
SIDEBAND_ENHANCED_WEIGHTS = {
    'core': 0.3,      # 主峰区域权重降低
    'sideband': 0.6,  # 边带区域权重大幅提高
    'base': 0.1       # 基底区域权重较低
}

# 传统均衡配置（对比用）
TRADITIONAL_WEIGHTS = {
    'core': 0.5,
    'sideband': 0.3,
    'base': 0.2
}
```

### 3.2 自适应权重调整
根据光谱特性自动调整权重：
- 高质量锁模状态：增加边带权重
- 边界状态：平衡各区域权重
- 非锁模状态：增加主峰权重

## 4. 实施步骤

1. **数据预处理**: 确保目标光谱和测量光谱的波长对齐
2. **区域识别**: 自动识别主峰、边带和基底区域
3. **分区域计算**: 分别计算各区域的MSE和相关系数
4. **加权合并**: 根据配置权重合并各区域损失
5. **结果分析**: 提供详细的分区域分析结果

## 5. 预期优势

### 5.1 相比传统方法的优势
1. **边带敏感性**: 更好地捕捉边带区域的细微变化
2. **锁模判别精度**: 提高锁模/非锁模状态的判别准确性
3. **噪声鲁棒性**: 通过分区域处理提高对噪声的鲁棒性
4. **物理意义**: 更符合锁模激光器的物理特性

### 5.2 应用场景
1. **实时锁模监测**: 在线监测锁模状态变化
2. **参数优化**: 指导EPC等参数的自动调节
3. **质量评估**: 量化锁模质量的不同方面
4. **故障诊断**: 识别锁模失效的具体原因

## 6. 下一步工作

1. 实现完整的代码框架
2. 使用实际数据进行验证和调优
3. 与传统方法进行对比分析
4. 集成到现有的损失函数框架中
