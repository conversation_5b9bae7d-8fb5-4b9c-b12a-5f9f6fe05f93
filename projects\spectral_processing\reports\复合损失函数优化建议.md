# 复合损失函数优化建议

## 现状分析

### 🔍 现有复合损失函数组成
1. **MSE (均方误差)** - 基础的强度差异
2. **皮尔逊相关系数** - 整体形状相关性
3. **质心波长差异** - 光谱中心位置差异

### 🎯 新增边带特性损失函数
基于我们的边带特性提取算法，新增6个子损失函数：
1. **边带数量差异** - 锁模稳定性指标
2. **边带能量差异** - 边带抑制效果指标
3. **边带幅度差异** - 峰值抑制程度指标
4. **边带不对称性差异** - 锁模平衡性指标
5. **边带抑制比差异** - 工程质量指标
6. **边带分布跨度差异** - 频域特性指标

## 🚀 优化策略

### 策略1: 分层损失设计

#### 第一层：基础光谱损失 (权重60%)
```
L_spectral = w_mse × L_mse + w_pearson × L_pearson + w_centroid × L_centroid

推荐权重：
- w_mse = 0.25 (25%)        # 基础强度差异，权重最高
- w_pearson = 0.20 (20%)    # 整体形状相关性
- w_centroid = 0.15 (15%)   # 光谱中心位置
```

**物理意义**：
- 确保基本的光谱形状匹配
- MSE关注逐点强度差异
- 皮尔逊相关关注整体形状相似性
- 质心差异关注光谱中心位置

#### 第二层：边带特性损失 (权重40%)
```
L_sideband = w_count × L_count + w_energy × L_energy + w_amplitude × L_amplitude +
             w_asymmetry × L_asymmetry + w_suppression × L_suppression + w_span × L_span

推荐权重：
- w_count = 0.08 (8%)       # 边带数量差异
- w_energy = 0.12 (12%)     # 边带能量差异，权重最高
- w_amplitude = 0.08 (8%)   # 边带幅度差异
- w_asymmetry = 0.04 (4%)   # 边带不对称性差异
- w_suppression = 0.06 (6%) # 边带抑制比差异
- w_span = 0.02 (2%)        # 边带分布跨度差异
```

**物理意义**：
- 专门关注锁模质量差异
- 边带能量是最重要的锁模质量指标
- 边带数量和幅度是核心特征
- 其他特性提供补充信息

#### 总损失函数
```
L_total = L_spectral + L_sideband
        = 0.60 × L_spectral + 0.40 × L_sideband
```

### 策略2: 自适应权重调整

#### 基于光谱质量的动态权重
```python
def adaptive_weights(spectral_quality_score):
    """
    根据光谱质量动态调整权重
    
    Args:
        spectral_quality_score: 0-1之间，1表示高质量光谱
    """
    if spectral_quality_score > 0.8:
        # 高质量光谱，更关注边带特性
        spectral_weight = 0.50
        sideband_weight = 0.50
    elif spectral_quality_score > 0.6:
        # 中等质量光谱，平衡考虑
        spectral_weight = 0.60
        sideband_weight = 0.40
    else:
        # 低质量光谱，更关注基础匹配
        spectral_weight = 0.70
        sideband_weight = 0.30
    
    return spectral_weight, sideband_weight
```

#### 基于应用场景的权重调整
```python
def scenario_based_weights(scenario):
    """根据应用场景调整权重"""
    
    if scenario == "锁模质量评估":
        # 重点关注边带特性
        return {"spectral": 0.40, "sideband": 0.60}
    
    elif scenario == "光谱形状匹配":
        # 重点关注整体形状
        return {"spectral": 0.80, "sideband": 0.20}
    
    elif scenario == "综合评估":
        # 平衡考虑
        return {"spectral": 0.60, "sideband": 0.40}
```

### 策略3: 新增专门的子损失函数

#### 3.1 边带频率特性损失
```python
def sideband_frequency_loss(target_spectrum, measured_spectrum):
    """
    边带频率特性损失
    关注边带的频率分布特征
    """
    # 计算边带的频率间隔
    target_intervals = compute_sideband_intervals(target_spectrum)
    measured_intervals = compute_sideband_intervals(measured_spectrum)
    
    # 频率间隔差异
    interval_loss = np.mean(np.abs(target_intervals - measured_intervals))
    
    return interval_loss
```

#### 3.2 边带相位特性损失
```python
def sideband_phase_loss(target_spectrum, measured_spectrum):
    """
    边带相位特性损失
    通过希尔伯特变换分析相位特征
    """
    from scipy.signal import hilbert
    
    # 提取边带光谱的相位信息
    target_phase = np.angle(hilbert(target_sideband_spectrum))
    measured_phase = np.angle(hilbert(measured_sideband_spectrum))
    
    # 相位差异
    phase_loss = np.mean(np.abs(np.unwrap(target_phase) - np.unwrap(measured_phase)))
    
    return phase_loss
```

#### 3.3 边带调制深度损失
```python
def sideband_modulation_depth_loss(target_spectrum, measured_spectrum):
    """
    边带调制深度损失
    评估边带的调制深度差异
    """
    # 计算调制深度
    target_modulation = (I_max - I_min) / (I_max + I_min)
    measured_modulation = (I_max - I_min) / (I_max + I_min)
    
    # 调制深度差异
    modulation_loss = abs(target_modulation - measured_modulation)
    
    return modulation_loss
```

#### 3.4 边带包络形状损失
```python
def sideband_envelope_loss(target_spectrum, measured_spectrum):
    """
    边带包络形状损失
    分析边带的整体包络形状
    """
    # 提取边带包络
    target_envelope = extract_sideband_envelope(target_spectrum)
    measured_envelope = extract_sideband_envelope(measured_spectrum)
    
    # 包络形状相关性
    envelope_corr, _ = pearsonr(target_envelope, measured_envelope)
    envelope_loss = 1 - envelope_corr
    
    return envelope_loss
```

## 📊 推荐的最终复合损失函数

### 完整损失函数公式
```
L_total = L_spectral + L_sideband + L_advanced

其中：

L_spectral = 0.25×L_mse + 0.20×L_pearson + 0.15×L_centroid

L_sideband = 0.08×L_count + 0.12×L_energy + 0.08×L_amplitude +
             0.04×L_asymmetry + 0.06×L_suppression + 0.02×L_span

L_advanced = 0.03×L_frequency + 0.02×L_modulation (可选)
```

### 权重分配表

| 损失类型 | 子损失项 | 权重 | 累计权重 | 物理意义 |
|----------|----------|------|----------|----------|
| **基础光谱** | MSE | 0.25 | 25% | 基础强度差异 |
| | 皮尔逊相关 | 0.20 | 45% | 整体形状相关性 |
| | 质心差异 | 0.15 | 60% | 光谱中心位置 |
| **边带特性** | 边带能量 | 0.12 | 72% | 最重要的锁模指标 |
| | 边带数量 | 0.08 | 80% | 锁模稳定性 |
| | 边带幅度 | 0.08 | 88% | 峰值抑制程度 |
| | 边带抑制比 | 0.06 | 94% | 工程质量指标 |
| | 边带不对称性 | 0.04 | 98% | 锁模平衡性 |
| | 边带分布跨度 | 0.02 | 100% | 频域特性 |

## 🎯 实施建议

### 阶段1: 基础整合 (立即实施)
1. **整合现有损失函数**：将MSE、皮尔逊相关、质心差异作为基础层
2. **添加边带特性损失**：集成6个边带特性子损失函数
3. **设置初始权重**：使用推荐的权重分配
4. **验证效果**：在现有数据集上测试性能

### 阶段2: 优化调整 (后续优化)
1. **权重敏感性分析**：分析不同权重对结果的影响
2. **自适应权重**：根据光谱质量动态调整权重
3. **场景化配置**：为不同应用场景设计专门的权重配置

### 阶段3: 高级功能 (可选扩展)
1. **频率特性损失**：添加边带频率分布分析
2. **相位特性损失**：引入相位信息分析
3. **调制深度损失**：评估边带调制特征
4. **包络形状损失**：分析边带整体包络

## 🔬 预期效果

### 相比现有复合损失函数的改进
1. **更全面的评估**：同时考虑光谱形状和边带质量
2. **更高的敏感性**：能够检测到细微的边带差异
3. **更强的物理意义**：每个子损失都对应明确的物理特征
4. **更好的控制指导**：为激光器参数优化提供更精确的反馈

### 量化改进预期
- **检测精度提升**：预计提升30-50%的边带质量差异检测能力
- **控制稳定性**：更稳定的损失函数梯度，有利于控制算法收敛
- **适应性增强**：能够适应不同类型的光谱和应用场景

这个增强的复合损失函数将为锁模激光器的智能控制提供更强大、更精确的评估工具。
