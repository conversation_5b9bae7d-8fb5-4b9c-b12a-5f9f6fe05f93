#!/usr/bin/env python3
"""
验证安装和参数计算功能
快速验证脚本，确保所有模块正常工作
"""

import sys
import os
from pathlib import Path

def main():
    print("🔬 锁模激光器参数计算工具验证")
    print("=" * 50)
    
    # 检查Python路径
    laser_path = Path('laser')
    if laser_path.exists():
        sys.path.append(str(laser_path))
        print("✅ 找到laser模块目录")
    else:
        print("❌ 未找到laser模块目录")
        return False
    
    # 检查核心模块
    try:
        from data_recovery import DataRecovery
        from parameter_calculator import ParameterCalculator
        print("✅ 核心模块导入成功")
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    
    # 检查数据文件
    data_folders = list(Path('.').glob('data724_*/multimodal_data/data_*'))
    if data_folders:
        print(f"✅ 找到 {len(data_folders)} 个数据文件夹")
        test_folder = data_folders[0]
        print(f"   使用测试数据: {test_folder}")
    else:
        print("❌ 未找到实验数据文件夹")
        return False
    
    # 测试数据恢复
    try:
        recovery = DataRecovery('.')
        data = recovery.load_real_multimodal_data(test_folder, 0)
        print("✅ 数据恢复功能正常")
        
        # 检查数据完整性
        required_keys = ['spectrum', 'autocorr', 'polarization', 'epc']
        missing_keys = [key for key in required_keys if key not in data]
        if missing_keys:
            print(f"⚠️  缺少数据类型: {missing_keys}")
        else:
            print("✅ 多模态数据完整")
            
    except Exception as e:
        print(f"❌ 数据恢复失败: {e}")
        return False
    
    # 测试参数计算
    try:
        calculator = ParameterCalculator(
            cavity_length=113.56,
            edf_length=2.5,
            smf1_length=100.0,
            smf2_length=11.06
        )
        params = calculator.calculate_parameters_from_real_data(data)
        print("✅ 参数计算功能正常")
        
        # 验证参数合理性
        checks = [
            ("β₂(SMF)", params.beta, -1e-4, -1e-6),
            ("γ(SMF)", params.gama, 5e-7, 5e-6),
            ("λc", params.lambda_c, 1.5, 1.6),
        ]
        
        all_reasonable = True
        for name, value, min_val, max_val in checks:
            if min_val <= value <= max_val:
                print(f"✅ {name}: {value:.2e} (合理)")
            else:
                print(f"⚠️  {name}: {value:.2e} (可能异常)")
                all_reasonable = False
        
        if all_reasonable:
            print("✅ 所有参数在合理范围内")
        
    except Exception as e:
        print(f"❌ 参数计算失败: {e}")
        return False
    
    # 输出推荐参数
    print("\n🎯 推荐FiberLaser_Pure.m参数:")
    print("-" * 40)
    print(f"beta_g = {params.beta_g:.6e};  %% EDF色散")
    print(f"gama_g = {params.gama_g:.6e};  %% EDF非线性系数")
    print(f"beta = {params.beta:.6e};    %% SMF色散")
    print(f"gama = {params.gama:.6e};    %% SMF非线性系数")
    print(f"lambda_c = {params.lambda_c:.6f};    %% 中心波长")
    
    print("\n📋 验证总结:")
    print("-" * 40)
    print("✅ 所有核心功能正常工作")
    print("✅ 参数计算基于严格物理理论")
    print("✅ 可以开始使用工具进行参数计算")
    
    print("\n📚 下一步:")
    print("1. 查看详细报告: laser/PHYSICS_BASED_PARAMETER_CALCULATION_REPORT.md")
    print("2. 使用推荐参数运行 FiberLaser_Pure.m")
    print("3. 对比仿真结果与实验数据")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 验证完成！工具已准备就绪。")
    else:
        print("\n❌ 验证失败，请检查安装和数据文件。")
        sys.exit(1)
