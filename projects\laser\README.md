# 光纤激光器仿真与参数优化项目

## 项目概述
本项目专注于模锁光纤激光器的仿真建模、参数导入验证、光谱对比分析与系统参数优化。

## 项目状态
- ✅ Stage 1: 参数导入和验证 (完成)
- ✅ Stage 2: 光谱对比和分辨率优化 (完成)  
- 🔄 Stage 3: 参数优化 (进行中)

## 核心组件

### 主要目录结构
```
laser/                          # 核心激光器仿真代码
├── core/                       # 核心仿真模块
├── config/                     # 配置文件
├── data/                       # 实验数据
└── README.md                   # 详细文档

stage1_parameter_import/        # 参数导入和验证
├── parameters/                 # 参数文件
├── validation/                 # 验证脚本
└── reports/                    # 验证报告

stage2_spectrum_comparison/     # 光谱对比分析
├── experimental_data/          # 实验光谱数据
├── simulation_results/         # 仿真结果
├── comparisons/               # 对比脚本
└── visualizations/            # 可视化结果

stage3_parameter_optimization/  # 参数优化
├── optimizers/                # 优化算法
├── results/                   # 优化结果
└── logs/                      # 优化日志
```

### 关键文件
- `main_simulation.m` - 主仿真脚本
- `run_tests.m` - 测试运行脚本
- `test_physics_calculation.py` - 物理计算验证
- `verify_physics_fix.py` - 物理公式验证
- `analyze_current_parameters_and_data.py` - 参数数据分析
- `recalculate_fiber_parameters_from_experimental_data.py` - 光纤参数重计算
- `real_data_parameter_calculator.py` - 实际数据参数计算器

## 快速开始

### Stage 1: 参数导入
```matlab
cd stage1_parameter_import/validation
run('import_stage1_parameters.m')
```

### Stage 2: 光谱对比
```matlab
cd stage2_spectrum_comparison/comparisons
run('target_vs_simulation_comparison.m')
```

### Stage 3: 参数优化
```matlab
cd stage3_parameter_optimization
% 即将开始...
```

### Python 工具使用
```python
# 验证物理计算
python test_physics_calculation.py

# 参数分析
python analyze_current_parameters_and_data.py

# 实验数据参数计算
python real_data_parameter_calculator.py
```

## 数据文件
- `experimental_spectrum*.mat` - 实验光谱数据
- `comprehensive_spectrum_analysis.mat` - 综合光谱分析
- `adjusted_simulation_parameters.mat` - 调整后的仿真参数

## 结果文件
- `results/all_data724_spectrum_comparison*.png` - 光谱对比图
- `results/comprehensive_spectrum_*.png` - 综合光谱分析图
- `results/experimental_spectrum_*.png` - 实验光谱图

## 技术特点
- 基于MATLAB的高精度光纤激光器仿真
- Python辅助的参数验证与数据处理
- 实验-仿真光谱对比框架
- 系统性参数优化流程

## 相关文档
- `laser/README.md` - 详细技术文档
- `laser/PROJECT_STATUS.md` - 项目状态报告
- `documentation/technical_reports/` - 技术报告
- `documentation/progress_reports/` - 进度报告

## 依赖要求
- MATLAB (推荐 R2020b 或更高版本)
- Python 3.8+
- NumPy, SciPy, Matplotlib
- 相关光学仿真工具包

## 联系信息
项目维护者：[您的姓名]
最后更新：2025-08-11
