# 多项目光学仿真与数据处理工作区 - 项目总览

## 工作区概述
本工作区包含三个主要的光学相关项目，涵盖激光器仿真、庞加莱球分析和光谱处理等领域。

## 项目分类

### 🔬 项目A: 光纤激光器仿真与参数优化 
**目录**: `projects/laser/` | **状态**: 🔄 进行中

专注于模锁光纤激光器的仿真建模、参数导入验证、光谱对比分析与系统参数优化。

**核心入口点**:
- `laser/` - 核心激光器仿真代码
- `stage1_parameter_import/` - 参数导入和验证
- `stage2_spectrum_comparison/` - 光谱对比分析  
- `stage3_parameter_optimization/` - 参数优化

**关键脚本**:
- `main_simulation.m` - 主仿真脚本
- `run_tests.m` - 测试运行脚本
- `test_physics_calculation.py` - 物理计算验证
- `analyze_current_parameters_and_data.py` - 参数数据分析

**快速开始**: 
```bash
cd projects/laser
cat README.md  # 查看详细说明
```

---

### 🌐 项目B: 庞加莱球数据点绘制与网格分析
**目录**: `projects/poincare/` | **状态**: ✅ 基本完成

专注于庞加莱球的数据可视化、经纬网格系统构建、交点选择与优化分析。

**核心入口点**:
- `multidata_724/` - 多数据集分析工具
- `sk_data/` - 偏振数据

**关键脚本**:
- `multidata_724/grid_based_meridian_parallel_viewer.py` - 网格经纬查看器
- `multidata_724/interactive_meridian_parallel_viewer.py` - 交互式查看器
- `multidata_724/optimized_intersection_viewer.py` - 优化交点查看器

**数据文件**: 所有 `*724*.pdf/png` 和相关 JSON/CSV 结果文件

**快速开始**:
```bash
cd projects/poincare  
cat README.md  # 查看详细说明
```

---

### 📊 项目C: 光谱信息处理与拟合分析
**目录**: `projects/spectral_processing/` | **状态**: ✅ 基本完成

专注于光谱数据的处理、拟合分析、峰值检测、边带分析和损失函数优化。

**核心入口点**:
- `comprehensive_loss/` - 综合损失函数分析
- `loss_function_data/` - 损失函数数据
- `weights/` - 权重配置
- `multimodal_data/` - 多模态数据

**关键脚本**:
- `advanced_spectrum_fitting.py` - 高级光谱拟合
- `comprehensive_sideband_detection.py` - 综合边带检测
- `final_sideband_enhanced_loss.py` - 最终边带增强损失
- `improved_selective_fitting.py` - 改进选择性拟合

**技术报告**: 所有中文 `.md` 报告文件 (复合损失函数、边带分析等)

**快速开始**:
```bash
cd projects/spectral_processing
cat README.md  # 查看详细说明
```

---

### 🛠️ 共享组件
**目录**: `utilities/`, `documentation/`, `archive/`, `core_simulation/`

跨项目的通用工具、文档和归档文件。

## 当前文件分布状态

### ⚠️ 注意事项
目前大部分文件仍位于工作区根目录。本分类系统提供了清晰的项目视图，但文件的物理移动将在后续步骤中进行。

### 📋 分类清单
详细的文件分类映射请参考: `PROJECT_CLASSIFICATION.json`

## 重组计划

### Step A: 项目分类与入口创建 ✅ (当前步骤)
- [x] 创建项目分类清单 (`PROJECT_CLASSIFICATION.json`)
- [x] 创建项目入口目录 (`projects/`)
- [x] 创建各项目README文档
- [x] 创建总览文档 (本文件)

### Step B: 温和文件移动 (下一步)
- [ ] 移动结果文件 (PDF/PNG)
- [ ] 移动数据文件 (JSON/CSV/MAT)
- [ ] 移动技术报告 (MD文件)
- [ ] 保留代码文件在原位置

### Step C: 代码重构 (最后步骤)
- [ ] 移动代码文件到项目目录
- [ ] 批量修正路径引用
- [ ] 进行回归测试
- [ ] 更新文档

## 使用建议

### 当前阶段 (Step A)
1. 通过项目README了解各项目功能
2. 使用现有路径运行脚本
3. 参考分类清单了解文件归属

### 准备下一步
在进行Step B之前，建议：
1. 备份重要数据
2. 确认当前脚本运行正常
3. 记录关键路径依赖

## 技术栈概览
- **MATLAB**: 激光器仿真、光谱分析
- **Python**: 数据处理、可视化、算法开发
- **数据格式**: MAT, JSON, CSV, PNG, PDF
- **可视化**: Matplotlib, 科学绘图

## 联系信息
项目维护者：[您的姓名]
创建日期：2025-08-11
分类版本：1.0

---

## 快速导航
- [激光器项目详情](projects/laser/README.md)
- [庞加莱球项目详情](projects/poincare/README.md)  
- [光谱处理项目详情](projects/spectral_processing/README.md)
- [详细分类清单](PROJECT_CLASSIFICATION.json)
- [原有项目结构](PROJECT_STRUCTURE.json)
- [原有组织文档](README_ORGANIZED.md)
