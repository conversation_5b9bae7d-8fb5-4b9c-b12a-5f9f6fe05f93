{"project_name": "多项目光学仿真与数据处理工作区", "organization_date": "2025-08-11", "classification_version": "1.0", "description": "按项目分类的文件映射，用于逐步重组工作区", "projects": {"laser": {"name": "光纤激光器仿真与参数优化", "description": "模锁激光器仿真、参数导入验证、光谱对比与优化", "status": "active", "entry_points": ["laser/", "stage1_parameter_import/", "stage2_spectrum_comparison/", "stage3_parameter_optimization/"]}, "poincare": {"name": "庞加莱球数据点绘制与网格分析", "description": "庞加莱球可视化、经纬网格系统、交点选择与优化", "status": "active", "entry_points": ["multidata_724/", "sk_data/"]}, "spectral_processing": {"name": "光谱信息处理与拟合分析", "description": "光谱拟合、峰值检测、边带分析、损失函数优化", "status": "active", "entry_points": ["comprehensive_loss/", "loss_function_data/", "weights/"]}, "shared": {"name": "共享工具与文档", "description": "跨项目的通用工具、文档和归档", "status": "utility", "entry_points": ["utilities/", "documentation/", "archive/", "core_simulation/"]}}, "file_classification": {"laser": {"directories": ["laser/", "stage1_parameter_import/", "stage2_spectrum_comparison/", "stage3_parameter_optimization/"], "files": ["main_simulation.m", "run_tests.m", "test_physics_calculation.py", "verify_physics_fix.py", "analyze_current_parameters_and_data.py", "recalculate_fiber_parameters_from_experimental_data.py", "real_data_parameter_calculator.py"], "data_files": ["experimental_spectrum*.mat", "comprehensive_spectrum_analysis.mat", "adjusted_simulation_parameters.mat"], "results": ["results/all_data724_spectrum_comparison*.png", "results/comprehensive_spectrum_*.png", "results/experimental_spectrum_*.png"]}, "poincare": {"directories": ["multidata_724/", "sk_data/"], "files": ["grid_system_info.json", "meridian_parallel_grid_info.json", "optimized_line_catalog.json", "enhanced_selection_results.json", "improved_selection_results.json", "optimized_intersection_results.json", "unified_meridian_parallel_results.json", "fine_meridian_selection_results.csv", "fine_meridian_selection_results.json", "fine_parallel_selection_results.csv", "fine_parallel_selection_results.json", "meridian_selection_results.csv", "meridian_selection_results.json", "parallel_selection_results.csv", "parallel_selection_results.json", "ultra_fine_parallel_results.csv", "ultra_fine_parallel_results.json"], "pdf_results": ["clean_grid_system_724.pdf", "enhanced_grid_724.pdf", "fine_meridian_selection_724.pdf", "fine_parallel_selection_724.pdf", "focused_grid_724.pdf", "grid_based_system_724.pdf", "improved_meridian_parallel_724.pdf", "meridian_parallel_grid_724.pdf", "meridian_selection_724.pdf", "multi_dataset_poincare_724.pdf", "optimized_grid_724.pdf", "optimized_intersection_724.pdf", "parallel_selection_724.pdf", "poincare_sphere_data724_4.pdf", "refined_poincare_sphere.pdf", "selected_grid_points_724.pdf", "ultra_fine_parallel_selection_724.pdf", "unified_meridian_parallel_724.pdf"], "png_results": ["results/clean_grid_system_724.png", "results/enhanced_grid_724.png", "results/fine_meridian_selection_724.png", "results/fine_parallel_selection_724.png", "results/focused_grid_724.png", "results/grid_based_system_724.png", "results/improved_meridian_parallel_724.png", "results/meridian_parallel_grid_724.png", "results/meridian_selection_724.png", "results/multi_dataset_poincare_724.png", "results/optimized_grid_724.png", "results/optimized_intersection_724.png", "results/parallel_selection_724.png", "results/poincare_sphere_data724_4.png", "results/refined_poincare_sphere.png", "results/selected_grid_points_724.png", "results/ultra_fine_parallel_selection_724.png", "results/unified_meridian_parallel_724.png"]}, "spectral_processing": {"directories": ["comprehensive_loss/", "loss_function_data/", "weights/", "multimodal_data/"], "files": ["advanced_spectrum_fitting.py", "comprehensive_sideband_detection.py", "corrected_enhanced_loss_function.py", "corrected_sideband_loss.py", "enhanced_composite_loss_function.py", "enhanced_sideband_loss_function.py", "final_sideband_enhanced_loss.py", "final_sideband_loss_function.py", "fixed_spectrum_analysis.py", "hybrid_voigt_spline_fitting.py", "improved_selective_fitting.py", "improved_sideband_analysis.py", "optimized_sideband_loss.py", "optimized_sideband_loss_final.py", "sideband_analysis_demo.py", "sideband_characteristic_loss.py", "sideband_detection_demo.py", "sideband_enhanced_loss.py", "sideband_visualization.py", "selective_fitting_strategy.py", "simplified_composite_loss_function.py", "spectrum_visualization.py"], "results": ["results/advanced_spectrum_fitting_comparison.png", "results/comprehensive_sideband_detection.png", "results/corrected_sideband_loss_analysis.png", "results/detailed_fitting_visualization.png", "results/enhanced_composite_loss_analysis.png", "results/enhanced_sideband_loss_analysis.png", "results/fitting_comparison_visualization.png", "results/fixed_spectrum_analysis.png", "results/hybrid_voigt_spline_fitting.png", "results/improved_selective_fitting.png", "results/improved_sideband_analysis.png", "results/selective_fitting_strategy.png", "results/selective_vs_traditional_fitting.png", "results/sideband_characteristic_loss_analysis.png", "results/sideband_detection_result.png", "results/sideband_enhanced_analysis.png", "results/sideband_separation_demo.png", "results/simplified_composite_loss_analysis.png", "results/spectrum_comparison_analysis.png"], "reports": ["复合损失函数优化完成报告.md", "复合损失函数优化建议.md", "复合损失函数设计.md", "拟合方式验证报告.md", "最终损失函数优化完成报告.md", "边带增强损失函数分析报告.md", "边带增强损失函数设计方案.md", "边带特性对比损失函数完成报告.md", "边带特性损失函数最终完成报告.md", "选择性拟合优化完成报告.md", "高斯目标光谱边带分析解决方案.md"]}, "shared": {"directories": ["utilities/", "documentation/", "archive/", "core_simulation/", "tests/", "config/", "core/", "visualization/"], "files": ["verify_installation.py", "PROJECT_STRUCTURE.json", "README_ORGANIZED.md", "USAGE_GUIDE.md", "项目完成总结.md", "项目理解.md"], "data_directories": ["data/", "data724_1/", "data724_2/", "data724_3/", "data724_4/"], "results": ["results/Figure_1.png", "results/test_poincare_data724_4.png"]}}, "reorganization_plan": {"step_a": {"description": "创建项目分类清单和入口README，不移动文件", "actions": ["创建 PROJECT_CLASSIFICATION.json", "创建 projects/laser/README.md", "创建 projects/poincare/README.md", "创建 projects/spectral_processing/README.md", "创建 PROJECTS_INDEX.md 总览"]}, "step_b": {"description": "移动结果/文档/数据文件，保留代码在原位", "priority": "low_risk_files_first"}, "step_c": {"description": "代码重构和路径修正", "priority": "high_risk_requires_testing"}}}