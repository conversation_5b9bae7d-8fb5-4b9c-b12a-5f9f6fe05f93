# 光谱信息处理与拟合分析项目

## 项目概述
本项目专注于光谱数据的处理、拟合分析、峰值检测、边带分析和损失函数优化。提供了多种先进的光谱分析算法和可视化工具。

## 项目状态
- ✅ 基础光谱拟合 (完成)
- ✅ 边带检测与分析 (完成)
- ✅ 损失函数优化 (完成)
- ✅ 选择性拟合策略 (完成)
- 🔄 高级复合分析 (进行中)

## 核心组件

### 主要目录结构
```
comprehensive_loss/             # 综合损失函数分析
├── core/                      # 核心算法
├── experiments/               # 实验脚本
├── results/                   # 分析结果
└── docs/                      # 文档

loss_function_data/            # 损失函数数据
├── boundary/                  # 边界数据
├── mode_locked/               # 锁模数据
├── non_mode_locked/           # 非锁模数据
└── targets/                   # 目标数据

weights/                       # 权重配置
├── optimized_weights.json     # 优化权重
├── target_data.json          # 目标数据
└── weights_config.json       # 权重配置

multimodal_data/              # 多模态数据
└── data_20250703_*/          # 时间序列数据
```

### 核心算法文件

#### 光谱拟合算法
- `advanced_spectrum_fitting.py` - 高级光谱拟合
- `hybrid_voigt_spline_fitting.py` - 混合Voigt样条拟合
- `improved_selective_fitting.py` - 改进选择性拟合
- `selective_fitting_strategy.py` - 选择性拟合策略
- `fixed_spectrum_analysis.py` - 固定光谱分析

#### 边带检测与分析
- `comprehensive_sideband_detection.py` - 综合边带检测
- `sideband_analysis_demo.py` - 边带分析演示
- `sideband_detection_demo.py` - 边带检测演示
- `improved_sideband_analysis.py` - 改进边带分析
- `sideband_visualization.py` - 边带可视化

#### 损失函数优化
- `corrected_enhanced_loss_function.py` - 修正增强损失函数
- `corrected_sideband_loss.py` - 修正边带损失
- `enhanced_composite_loss_function.py` - 增强复合损失函数
- `enhanced_sideband_loss_function.py` - 增强边带损失函数
- `final_sideband_enhanced_loss.py` - 最终边带增强损失
- `final_sideband_loss_function.py` - 最终边带损失函数
- `optimized_sideband_loss.py` - 优化边带损失
- `optimized_sideband_loss_final.py` - 最终优化边带损失
- `sideband_characteristic_loss.py` - 边带特性损失
- `sideband_enhanced_loss.py` - 边带增强损失
- `simplified_composite_loss_function.py` - 简化复合损失函数

#### 可视化工具
- `spectrum_visualization.py` - 光谱可视化

## 分析结果

### 可视化结果 (results/)
- `advanced_spectrum_fitting_comparison.png` - 高级光谱拟合对比
- `comprehensive_sideband_detection.png` - 综合边带检测
- `corrected_sideband_loss_analysis.png` - 修正边带损失分析
- `detailed_fitting_visualization.png` - 详细拟合可视化
- `enhanced_composite_loss_analysis.png` - 增强复合损失分析
- `enhanced_sideband_loss_analysis.png` - 增强边带损失分析
- `fitting_comparison_visualization.png` - 拟合对比可视化
- `fixed_spectrum_analysis.png` - 固定光谱分析
- `hybrid_voigt_spline_fitting.png` - 混合Voigt样条拟合
- `improved_selective_fitting.png` - 改进选择性拟合
- `improved_sideband_analysis.png` - 改进边带分析
- `selective_fitting_strategy.png` - 选择性拟合策略
- `selective_vs_traditional_fitting.png` - 选择性vs传统拟合
- `sideband_characteristic_loss_analysis.png` - 边带特性损失分析
- `sideband_detection_result.png` - 边带检测结果
- `sideband_enhanced_analysis.png` - 边带增强分析
- `sideband_separation_demo.png` - 边带分离演示
- `simplified_composite_loss_analysis.png` - 简化复合损失分析
- `spectrum_comparison_analysis.png` - 光谱对比分析

### 技术报告
- `复合损失函数优化完成报告.md` - 复合损失函数优化报告
- `复合损失函数优化建议.md` - 复合损失函数优化建议
- `复合损失函数设计.md` - 复合损失函数设计
- `拟合方式验证报告.md` - 拟合方式验证报告
- `最终损失函数优化完成报告.md` - 最终损失函数优化报告
- `边带增强损失函数分析报告.md` - 边带增强损失函数分析报告
- `边带增强损失函数设计方案.md` - 边带增强损失函数设计方案
- `边带特性对比损失函数完成报告.md` - 边带特性对比损失函数报告
- `边带特性损失函数最终完成报告.md` - 边带特性损失函数最终报告
- `选择性拟合优化完成报告.md` - 选择性拟合优化报告
- `高斯目标光谱边带分析解决方案.md` - 高斯目标光谱边带分析方案

## 快速开始

### 基础光谱拟合
```python
python advanced_spectrum_fitting.py
```

### 边带检测分析
```python
python comprehensive_sideband_detection.py
python sideband_analysis_demo.py
```

### 损失函数优化
```python
python final_sideband_enhanced_loss.py
python optimized_sideband_loss_final.py
```

### 选择性拟合
```python
python improved_selective_fitting.py
python selective_fitting_strategy.py
```

### 可视化分析
```python
python spectrum_visualization.py
python sideband_visualization.py
```

## 技术特点
- 多种先进的光谱拟合算法
- 智能边带检测与分离
- 自适应损失函数优化
- 选择性拟合策略
- 高质量科学可视化
- 模块化设计，易于扩展

## 算法亮点
- **Hybrid Voigt-Spline拟合**: 结合Voigt函数和样条插值
- **边带增强损失函数**: 专门针对边带特性优化
- **选择性拟合策略**: 智能选择拟合区域和参数
- **复合损失函数**: 多目标优化框架
- **自适应权重调整**: 动态优化权重配置

## 依赖要求
- Python 3.8+
- NumPy, SciPy
- Matplotlib, Seaborn
- Pandas
- scikit-learn
- 光谱分析专用库

## 联系信息
项目维护者：[您的姓名]
最后更新：2025-08-11
